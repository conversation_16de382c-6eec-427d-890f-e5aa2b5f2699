/* TinyMCE Editor Styling */
.tinymce-editor-container {
  width: 100%;
  position: relative;
}

.editor-wrapper {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

/* TinyMCE Toolbar Styling */
.tox .tox-toolbar {
  background: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 8px 12px !important;
}

.tox .tox-toolbar__group {
  border: none !important;
}

.tox .tox-tbtn {
  border-radius: 4px !important;
  margin: 0 2px !important;
  color: #374151 !important;
}

.tox .tox-tbtn:hover {
  background: #e5e7eb !important;
  color: #1f2937 !important;
}

.tox .tox-tbtn--enabled {
  background: #3b82f6 !important;
  color: white !important;
}

.tox .tox-tbtn--enabled:hover {
  background: #2563eb !important;
}

/* Editor Content Area */
.tox .tox-edit-area {
  border: none !important;
}

.tox .tox-edit-area__iframe {
  background: white !important;
}

/* Status Bar */
.tox .tox-statusbar {
  background: #f9fafb !important;
  border-top: 1px solid #e5e7eb !important;
  color: #6b7280 !important;
  font-size: 12px !important;
  padding: 8px 12px !important;
}

/* Dropdown Menus */
.tox .tox-collection__item {
  color: #374151 !important;
}

.tox .tox-collection__item:hover {
  background: #f3f4f6 !important;
}

.tox .tox-collection__item--active {
  background: #3b82f6 !important;
  color: white !important;
}

/* Dialog Styling */
.tox .tox-dialog {
  border-radius: 8px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.tox .tox-dialog__header {
  background: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.tox .tox-dialog__title {
  color: #1f2937 !important;
  font-weight: 600 !important;
}

.tox .tox-button {
  border-radius: 6px !important;
  font-weight: 500 !important;
}

.tox .tox-button--primary {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

.tox .tox-button--primary:hover {
  background: #2563eb !important;
  border-color: #2563eb !important;
}

/* Loading State */
.tox .tox-throbber {
  background: rgba(255, 255, 255, 0.8) !important;
}

/* Focus States */
.tox.tox-tinymce:focus-within {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  border-color: #3b82f6 !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .tox .tox-toolbar {
    padding: 6px 8px !important;
  }
  
  .tox .tox-tbtn {
    margin: 0 1px !important;
    padding: 6px !important;
  }
  
  .tox .tox-toolbar__group {
    margin: 0 4px 0 0 !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .editor-wrapper {
    background: #1f2937;
    border-color: #374151;
  }
  
  .tox .tox-toolbar {
    background: #374151 !important;
    border-bottom-color: #4b5563 !important;
  }
  
  .tox .tox-tbtn {
    color: #e5e7eb !important;
  }
  
  .tox .tox-tbtn:hover {
    background: #4b5563 !important;
    color: #f9fafb !important;
  }
  
  .tox .tox-statusbar {
    background: #374151 !important;
    border-top-color: #4b5563 !important;
    color: #9ca3af !important;
  }
}

/* Custom Animations */
.tox .tox-toolbar,
.tox .tox-statusbar {
  transition: all 0.2s ease-in-out;
}

.tox .tox-tbtn {
  transition: all 0.15s ease-in-out;
}

/* WordPress-like Styling */
.tinymce-editor-container .editor-wrapper {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Statistics Display */
.editor-stats {
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  padding: 8px 12px;
  font-size: 12px;
  color: #6b7280;
  display: flex;
  gap: 16px;
  align-items: center;
}

.editor-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Loading Indicator */
.tinymce-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #f9fafb;
  color: #6b7280;
  font-size: 14px;
}

.tinymce-loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
